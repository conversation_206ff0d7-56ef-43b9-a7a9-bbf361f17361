#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HEIC转PNG脚本
从Mac备忘录获取选中的HEIC照片，转换为PNG格式并复制到剪切板
通过 Keyboard Maestro 触发
"""

import os
import sys
import tempfile
import subprocess
from pathlib import Path
from PIL import Image
import pillow_heif

def setup_heif_support():
    """
    设置HEIF格式支持
    """
    try:
        # 注册HEIF格式支持
        pillow_heif.register_heif_opener()
        return True
    except Exception as e:
        print(f"设置HEIF支持失败: {e}")
        return False

def check_clipboard_contents():
    """
    检查剪切板内容类型
    """
    try:
        result = subprocess.run(['osascript', '-e', 'clipboard info'],
                              capture_output=True, text=True)
        if result.returncode == 0:
            return result.stdout.strip()
        return "无法获取剪切板信息"
    except Exception as e:
        return f"检查剪切板失败: {e}"

def get_clipboard_image_data():
    """
    从剪切板获取图片数据
    """
    try:
        # 首先检查剪切板内容
        clipboard_info = check_clipboard_contents()
        print(f"🔍 剪切板内容类型: {clipboard_info}")

        # 尝试多种图片格式
        formats_to_try = [
            ('«class PNGf»', 'png'),
            ('«class HEIC»', 'heic'),
            ('«class JPEG»', 'jpg'),
            ('«class TIFF»', 'tiff'),
            ('«class pictPICT»', 'pict'),
            ('picture', 'picture')
        ]

        for format_class, format_name in formats_to_try:
            print(f"🔄 尝试获取 {format_name.upper()} 格式...")
            script = f'''
            try
                set clipboardData to the clipboard as {format_class}
                return clipboardData
            on error errMsg
                return "error: " & errMsg
            end try
            '''

            result = subprocess.run(['osascript', '-e', script],
                                  capture_output=True, text=False)

            if result.returncode == 0:
                output = result.stdout
                if not output.startswith(b'error:') and len(output) > 100:  # 图片数据应该比较大
                    print(f"✅ 成功获取 {format_name.upper()} 格式数据")
                    return output, format_name
                else:
                    error_msg = output.decode('utf-8', errors='ignore') if output.startswith(b'error:') else "数据太小"
                    print(f"❌ {format_name.upper()} 格式失败: {error_msg}")

        # 尝试使用pbpaste命令（备用方案）
        print("🔄 尝试使用pbpaste获取图片...")
        result = subprocess.run(['pbpaste'], capture_output=True)
        if result.returncode == 0 and len(result.stdout) > 100:
            # 尝试检测文件类型
            if result.stdout.startswith(b'\x89PNG'):
                return result.stdout, 'png'
            elif result.stdout.startswith(b'\xff\xd8\xff'):
                return result.stdout, 'jpg'
            elif result.stdout.startswith(b'RIFF') and b'WEBP' in result.stdout[:20]:
                return result.stdout, 'webp'

        return None, None

    except Exception as e:
        print(f"获取剪切板数据失败: {e}")
        return None, None



def convert_heic_to_png(image_data, input_format):
    """
    将HEIC数据转换为PNG格式
    """
    try:
        # 创建临时文件
        with tempfile.NamedTemporaryFile(suffix=f'.{input_format}', delete=False) as temp_input:
            temp_input.write(image_data)
            temp_input_path = temp_input.name
        
        # 使用PIL打开图片
        with Image.open(temp_input_path) as img:
            # 转换为RGB模式（PNG需要）
            if img.mode in ('RGBA', 'LA'):
                # 保持透明度
                png_img = img
            else:
                # 转换为RGB
                png_img = img.convert('RGB')
            
            # 保存为PNG到临时文件
            with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as temp_output:
                png_img.save(temp_output.name, 'PNG', optimize=True)
                temp_output_path = temp_output.name
        
        # 读取PNG数据
        with open(temp_output_path, 'rb') as f:
            png_data = f.read()
        
        # 清理临时文件
        os.unlink(temp_input_path)
        os.unlink(temp_output_path)
        
        return png_data
        
    except Exception as e:
        print(f"转换图片失败: {e}")
        return None

def copy_png_to_clipboard(png_data):
    """
    将PNG数据复制到剪切板
    """
    try:
        # 创建临时PNG文件
        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as temp_file:
            temp_file.write(png_data)
            temp_path = temp_file.name
        
        # 使用osascript将PNG复制到剪切板
        script = f'''
        set pngFile to POSIX file "{temp_path}"
        tell application "Finder"
            set the clipboard to (read pngFile as «class PNGf»)
        end tell
        '''
        
        result = subprocess.run(['osascript', '-e', script], 
                              capture_output=True, text=True)
        
        # 清理临时文件
        os.unlink(temp_path)
        
        if result.returncode == 0:
            return True
        else:
            print(f"复制到剪切板失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"复制PNG到剪切板失败: {e}")
        return False

def main():
    """
    主函数
    """
    print("🔄 开始HEIC转PNG处理...")
    print("📝 请确保已在备忘录中复制了HEIC图片到剪切板")

    # 设置HEIF支持
    if not setup_heif_support():
        print("❌ 无法设置HEIF支持，请确保已安装pillow-heif")
        return False

    # 从剪切板获取图片数据
    print("📋 从剪切板获取图片数据...")
    image_data, input_format = get_clipboard_image_data()

    if image_data is None:
        print("❌ 无法从剪切板获取图片数据")
        print("💡 请先在备忘录中选中图片并复制(⌘C)，然后重新运行脚本")
        return False

    print(f"✅ 获取到 {input_format.upper()} 格式图片，大小: {len(image_data)} 字节")

    # 如果已经是PNG格式，直接返回成功
    if input_format == 'png':
        print("✅ 图片已经是PNG格式，无需转换")
        return True

    # 转换为PNG
    print("🔄 转换为PNG格式...")
    png_data = convert_heic_to_png(image_data, input_format)

    if png_data is None:
        print("❌ 转换失败")
        return False

    print(f"✅ 转换成功，PNG大小: {len(png_data)} 字节")

    # 复制PNG到剪切板
    print("📋 复制PNG到剪切板...")
    if copy_png_to_clipboard(png_data):
        print("✅ 成功！PNG图片已复制到剪切板")
        return True
    else:
        print("❌ 复制到剪切板失败")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️  操作被用户取消")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 发生未预期的错误: {e}")
        sys.exit(1)
